<script lang="ts">
	import CardListEntry from '../CardListEntry.svelte';
	import type { Node, Route, NodeRoute } from '$lib/common/types';
	import { debug } from '$lib/common/debug'
	import NodeRouteComponent from './NodeRoute.svelte';
	import ToggleOff from '~icons/mdi/toggle-switch-variant-off';
	import ToggleOn from '~icons/mdi/toggle-switch-variant';
	import { approveNodeRoutes, getNodeRoutes } from '$lib/common/api';
	import { App } from '$lib/States.svelte';
	import type { Snippet } from 'svelte';

	type NodeRoutesProps = {
		node: Node,
		showTitle?: boolean,
		showDelete?: boolean,
		childBottom?: Snippet,
	}

	let {
		node = $bindable(),
		showTitle = true,
		showDelete = true,
		childBottom = undefined,
	}: NodeRoutesProps = $props()

	// Get routes directly from node data (v0.26+)
	const nodeRoutes = $derived.by(() => {
		return getNodeRoutes(node);
	});

	// Create legacy Route objects for backward compatibility
	const routes = $derived.by(() => {
		return nodeRoutes.map((nr, index) => {
			const route: Route = {
				id: `${nr.nodeId}-${index}`,
				createdAt: node.createdAt,
				deletedAt: '',
				node: node,
				machine: undefined as never,
				prefix: nr.prefix,
				advertised: nr.available,
				enabled: nr.approved,
				isPrimary: nr.subnet
			};
			return route;
		});
	});

	async function toggleAll(routes: Route[], state: 'on' | 'off') {
		try {
			if (state === 'on') {
				// Approve all available routes
				const routesToApprove = [...new Set([
					...(node.approvedRoutes || []),
					...routes.map(r => r.prefix)
				])];
				const updatedNode = await approveNodeRoutes(node.id, routesToApprove);
				// Update the node in the app state
				App.updateValue(App.nodes, updatedNode);
			} else {
				// Remove all routes from approved list
				const routesToRemove = routes.map(r => r.prefix);
				const remainingRoutes = (node.approvedRoutes || []).filter(r => !routesToRemove.includes(r));
				const updatedNode = await approveNodeRoutes(node.id, remainingRoutes);
				// Update the node in the app state
				App.updateValue(App.nodes, updatedNode);
			}
		} catch (e) {
			debug(e)
		}
	}
</script>

<CardListEntry title={showTitle ? "Routes:" : undefined} valueClasses="justify-right text-right" top>
	<div class="mb-2 flex flex-row">
		<button class="btn btn-sm items-end gap-1 px-0 ml-4 text-success-700 dark:text-success-400" onclick={() => {toggleAll(routes, 'on')}}>
			<ToggleOn />
		</button>
		<button class="btn btn-sm items-end gap-1 px-0 ml-4 text-error-600 dark:text-error-400" onclick={() => {toggleAll(routes, 'off')}}>
			<ToggleOff />
		</button>
	</div>
	{#if childBottom === undefined}
		{#each routes as _, i}
			<div class="grid grid-cols-12 col-span-12 font-thin">
				<NodeRouteComponent route={routes[i]} bind:showDelete {node} />
			</div>
		{/each}
	{:else}
		{@render childBottom()}
	{/if}
</CardListEntry>
