#!/usr/bin/env node

/**
 * 验证 headscale-admin v0.26+ 兼容性更改
 * 这个脚本检查所有必要的文件和更改是否正确实施
 */

import fs from 'fs';
import path from 'path';

const checks = [];

function addCheck(name, condition, message) {
  checks.push({ name, condition, message });
}

function runChecks() {
  console.log('🔍 验证 headscale-admin v0.26+ 兼容性更改...\n');
  
  let passed = 0;
  let failed = 0;
  
  checks.forEach(check => {
    try {
      if (check.condition()) {
        console.log(`✅ ${check.name}`);
        passed++;
      } else {
        console.log(`❌ ${check.name}: ${check.message}`);
        failed++;
      }
    } catch (error) {
      console.log(`❌ ${check.name}: 检查时出错 - ${error.message}`);
      failed++;
    }
  });
  
  console.log(`\n📊 检查结果: ${passed} 通过, ${failed} 失败`);
  
  if (failed === 0) {
    console.log('🎉 所有检查都通过了！headscale-admin 已成功更新以支持 v0.26+');
  } else {
    console.log('⚠️  有一些检查失败，请检查上述错误');
  }
  
  return failed === 0;
}

// 检查文件是否存在
addCheck(
  'API URL 文件存在',
  () => fs.existsSync('src/lib/common/api/url.ts'),
  'url.ts 文件不存在'
);

// 检查 URL 文件中的更改
addCheck(
  'API URL 文件包含新的路由端点函数',
  () => {
    const content = fs.readFileSync('src/lib/common/api/url.ts', 'utf8');
    return content.includes('getNodeApproveRoutesUrl') && !content.includes('API_URL_ROUTES');
  },
  'url.ts 文件未正确更新'
);

// 检查类型定义
addCheck(
  'Node 类型包含新的路由字段',
  () => {
    const content = fs.readFileSync('src/lib/common/types.ts', 'utf8');
    return content.includes('approvedRoutes?:') && 
           content.includes('availableRoutes?:') && 
           content.includes('subnetRoutes?:');
  },
  'Node 类型未包含新的路由字段'
);

// 检查 NodeRoute 类型
addCheck(
  'NodeRoute 类型已定义',
  () => {
    const content = fs.readFileSync('src/lib/common/types.ts', 'utf8');
    return content.includes('export type NodeRoute');
  },
  'NodeRoute 类型未定义'
);

// 检查 get.ts 更新
addCheck(
  'getRoutes 函数已更新',
  () => {
    const content = fs.readFileSync('src/lib/common/api/get.ts', 'utf8');
    return content.includes('getNodeRoutes') && content.includes('Legacy function for backward compatibility');
  },
  'get.ts 文件未正确更新'
);

// 检查 modify.ts 更新
addCheck(
  'approveNodeRoutes 函数已添加',
  () => {
    const content = fs.readFileSync('src/lib/common/api/modify.ts', 'utf8');
    return content.includes('export async function approveNodeRoutes');
  },
  'approveNodeRoutes 函数未添加到 modify.ts'
);

// 检查 NodeRoutes 组件更新
addCheck(
  'NodeRoutes 组件已更新',
  () => {
    const content = fs.readFileSync('src/lib/cards/node/NodeRoutes.svelte', 'utf8');
    return content.includes('getNodeRoutes') && content.includes('approveNodeRoutes');
  },
  'NodeRoutes 组件未正确更新'
);

// 检查 NodeRoute 组件更新
addCheck(
  'NodeRoute 组件已更新',
  () => {
    const content = fs.readFileSync('src/lib/cards/node/NodeRoute.svelte', 'utf8');
    return content.includes('toggleRoute') && content.includes('approveNodeRoutes');
  },
  'NodeRoute 组件未正确更新'
);

// 检查状态管理更新
addCheck(
  'States.svelte.ts 已更新',
  () => {
    const content = fs.readFileSync('src/lib/States.svelte.ts', 'utf8');
    return content.includes('In v0.26+, routes are derived from nodes');
  },
  'States.svelte.ts 未正确更新'
);

// 检查文档文件
addCheck(
  '迁移文档已创建',
  () => fs.existsSync('HEADSCALE_V026_MIGRATION.md'),
  '迁移文档不存在'
);

addCheck(
  '变更总结已创建',
  () => fs.existsSync('CHANGES_SUMMARY.md'),
  '变更总结不存在'
);

// 检查测试文件
addCheck(
  '测试文件已创建',
  () => fs.existsSync('src/lib/common/api/routes.test.ts'),
  '测试文件不存在'
);

// 运行所有检查
const success = runChecks();

if (success) {
  console.log('\n🚀 下一步:');
  console.log('1. 运行 npm run build 来构建项目');
  console.log('2. 测试与 headscale v0.26+ 的兼容性');
  console.log('3. 验证路由管理功能正常工作');
} else {
  console.log('\n🔧 请修复上述问题后重新运行验证');
}

process.exit(success ? 0 : 1);
