import { apiGet } from './base';
import { API_URL_ROUTES } from './url';

// Cache the version detection result to avoid repeated API calls
let versionCache: boolean | null = null;

/**
 * Detects if the headscale server is v0.26.0 or later
 * v0.26+ removed the dedicated routes API endpoints
 * @returns true if v0.26+, false if v0.25 or earlier
 */
export async function isHeadscaleV026OrLater(): Promise<boolean> {
	// Return cached result if available
	if (versionCache !== null) {
		return versionCache;
	}
	
	try {
		// Try to access the old routes API - if it succeeds, we're on v0.25 or earlier
		await apiGet(API_URL_ROUTES);
		versionCache = false; // v0.25 or earlier
		return false;
	} catch (error) {
		// If the routes API doesn't exist, we're on v0.26+
		versionCache = true; // v0.26+
		return true;
	}
}

/**
 * Reset the version cache (useful for testing or when switching servers)
 */
export function resetVersionCache(): void {
	versionCache = null;
}

/**
 * Get the cached version result without making an API call
 * @returns the cached version result, or null if not yet determined
 */
export function getCachedVersion(): boolean | null {
	return versionCache;
}
