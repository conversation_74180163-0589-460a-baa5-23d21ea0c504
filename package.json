{"name": "headscale-admin", "version": "0.0.1", "private": true, "scripts": {"dev": "vite dev", "build": "vite build", "preview": "vite preview", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch", "test": "vitest", "lint": "prettier --plugin-search-dir . --check . && eslint .", "format": "prettier --plugin-search-dir . --write ."}, "devDependencies": {"@iconify/json": "^2.2.311", "@skeletonlabs/skeleton": "^2.11.0", "@skeletonlabs/tw-plugin": "^0.4.1", "@sveltejs/adapter-auto": "^4.0.0", "@sveltejs/adapter-static": "^3.0.8", "@sveltejs/kit": "^2.17.3", "@sveltejs/vite-plugin-svelte": "^5.0.3", "@tailwindcss/forms": "0.5.10", "@tailwindcss/typography": "^0.5.16", "@types/node": "^22.13.5", "@typescript-eslint/eslint-plugin": "^8.25.0", "@typescript-eslint/parser": "^8.25.0", "autoprefixer": "10.4.20", "eslint": "^9.21.0", "eslint-config-prettier": "^10.0.2", "eslint-plugin-svelte": "^3.0.2", "postcss": "^8.5.3", "prettier": "^3.5.2", "prettier-plugin-svelte": "^3.3.3", "svelte": "^5.20.5", "svelte-check": "^4.1.4", "tailwindcss": "3.4.17", "tslib": "^2.8.1", "typescript": "^5.7.3", "vite": "^6.2.0", "vite-plugin-tailwind-purgecss": "0.3.5", "vitest": "^3.0.7"}, "type": "module", "dependencies": {"@floating-ui/dom": "^1.6.13", "async-mutex": "^0.5.0", "dompurify": "^3.2.4", "highlight.js": "11.11.1", "ipaddr.js": "^2.2.0", "js-xxhash": "^4.0.0", "json5": "^2.2.3", "svelte-jsoneditor": "^2.4.0", "unplugin-icons": "^22.1.0"}}