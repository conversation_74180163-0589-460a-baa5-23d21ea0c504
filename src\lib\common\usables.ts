
export function clickOutside(node: H<PERSON><PERSON>lement, callback: () => void): { destroy: () => void} {
    const handleClick = (event: MouseEvent) => {
        if (!node.contains(event.target as Node)) {
            callback();
        }
    };

    document.addEventListener('click', handleClick, true);

    return {
        destroy: () => {
            document.removeEventListener('click', handleClick, true)
        }
    }
}