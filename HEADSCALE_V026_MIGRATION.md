# Headscale v0.26+ Migration Guide

This document describes the changes made to headscale-admin to support headscale v0.26.0 and later versions.

## Overview

Headscale v0.26.0 introduced breaking changes to the Routes API:

- **Routes API removed**: The dedicated `/api/v1/routes` endpoints no longer exist
- **Routes managed via Node API**: Routes are now managed through `/api/v1/node/{nodeId}/approve_routes`
- **Route data embedded in Node objects**: Route information is now included directly in Node objects

## Changes Made

### 1. API URL Updates (`src/lib/common/api/url.ts`)

- Removed `API_URL_ROUTES` constant
- Added `getNodeApproveRoutesUrl()` helper function for the new route approval endpoint

### 2. Type Definitions (`src/lib/common/types.ts`)

- Updated `Node` type to include new route fields:
  - `approvedRoutes?: string[]` - Routes that are approved for this node
  - `availableRoutes?: string[]` - Routes that are available but not yet approved
  - `subnetRoutes?: string[]` - Subnet routes advertised by this node
- Added `NodeRoute` type for the new route representation
- Kept legacy `Route` type for backward compatibility

### 3. API Functions

#### Get Routes (`src/lib/common/api/get.ts`)
- Updated `getRoutes()` to extract route information from node data
- Added `getNodeRoutes()` helper function to convert node data to route objects

#### Modify Routes (`src/lib/common/api/modify.ts`)
- Added `approveNodeRoutes()` function for the new v0.26+ API
- Updated `enableRoute()` and `disableRoute()` to use the new node-based API

#### Delete Routes (`src/lib/common/api/delete.ts`)
- Updated `deleteRoute()` to remove routes from the node's approved routes list

### 4. Component Updates

#### NodeRoutes Component (`src/lib/cards/node/NodeRoutes.svelte`)
- Updated to use route data directly from node objects
- Modified toggle functions to use the new `approveNodeRoutes()` API

#### NodeRoute Component (`src/lib/cards/node/NodeRoute.svelte`)
- Updated route toggle functionality to use the new node-based API
- Improved state management to update both local and global state

### 5. State Management (`src/lib/States.svelte.ts`)

- Updated `populateRoutes()` to handle the dependency on node data
- Modified `populateAll()` to load nodes before routes (since routes depend on nodes in v0.26+)

## Backward Compatibility

The changes maintain backward compatibility by:

1. **Legacy Route objects**: The `getRoutes()` function still returns an array of `Route` objects, but they are now synthesized from node data
2. **Existing components**: Most existing components continue to work without modification
3. **API interface**: The public API interface remains the same, with implementation changes hidden behind the existing functions

## New Features in v0.26+

### Route Management
- Routes are now managed per-node through the Node API
- Approving exit routes (0.0.0.0/0 or ::/0) automatically approves both IPv4 and IPv6
- Only routes accessible to a node are sent to that node

### CLI Changes
The headscale CLI has also changed:

```bash
# List routes (new format)
headscale nodes list-routes

# Approve routes for a node
headscale nodes approve-routes --identifier 1 --routes 0.0.0.0/0,::/0
```

## Testing

To test the migration:

1. **Start headscale v0.26+** with some nodes that advertise routes
2. **Access headscale-admin** and verify that:
   - Routes are displayed correctly in the Routes page
   - Route toggle functionality works
   - Route approval/disapproval updates the node state
   - No console errors occur

## Troubleshooting

### Common Issues

1. **Routes not showing**: Ensure nodes have `availableRoutes` or `approvedRoutes` data
2. **Toggle not working**: Check that the node ID is correct and the API endpoint is accessible
3. **State not updating**: Verify that the node state is being updated in the global app state

### Debug Information

Enable debug mode in the application settings to see detailed API calls and state changes.

## Migration Checklist

- [x] Update API URL definitions
- [x] Add new route fields to Node type
- [x] Update getRoutes() function
- [x] Add approveNodeRoutes() function
- [x] Update route enable/disable functions
- [x] Update route delete function
- [x] Update NodeRoutes component
- [x] Update NodeRoute component
- [x] Update state management
- [x] Maintain backward compatibility
- [x] Test with headscale v0.26+

## Future Improvements

1. **Performance optimization**: Consider caching route data to avoid repeated calculations
2. **Real-time updates**: Implement WebSocket or polling for real-time route status updates
3. **Bulk operations**: Add support for bulk route approval/disapproval
4. **Route validation**: Add client-side validation for route formats
