# 🔄 Headscale-Admin 双版本兼容性

## 📋 概述

headscale-admin 现在同时支持：
- **headscale v0.25.x** (使用原始的 Routes API)
- **headscale v0.26.0+** (使用新的基于节点的 Routes API)

应用程序会自动检测 headscale 服务器版本并使用相应的 API。

## 🔍 版本检测机制

### 自动检测
应用程序通过尝试访问 `/api/v1/routes` 端点来检测版本：
- **成功访问** → headscale v0.25.x (使用旧的 Routes API)
- **访问失败** → headscale v0.26.0+ (使用新的基于节点的 API)

### 缓存机制
- 版本检测结果会被缓存，避免重复的 API 调用
- 可以通过 `resetVersionCache()` 函数重置缓存

## 🔧 API 兼容性映射

### 获取路由 (`getRoutes()`)

#### v0.25.x 行为
```typescript
// 直接调用原始 API
GET /api/v1/routes
```

#### v0.26.0+ 行为
```typescript
// 从节点数据中提取路由信息
GET /api/v1/node  // 获取所有节点
// 然后从节点的 approvedRoutes, availableRoutes, subnetRoutes 字段提取路由
```

### 启用路由 (`enableRoute()`)

#### v0.25.x 行为
```typescript
POST /api/v1/routes/{routeId}/enable
```

#### v0.26.0+ 行为
```typescript
POST /api/v1/node/{nodeId}/approve_routes
// 将路由添加到节点的批准路由列表
```

### 禁用路由 (`disableRoute()`)

#### v0.25.x 行为
```typescript
POST /api/v1/routes/{routeId}/disable
```

#### v0.26.0+ 行为
```typescript
POST /api/v1/node/{nodeId}/approve_routes
// 从节点的批准路由列表中移除路由
```

### 删除路由 (`deleteRoute()`)

#### v0.25.x 行为
```typescript
DELETE /api/v1/routes/{routeId}
```

#### v0.26.0+ 行为
```typescript
POST /api/v1/node/{nodeId}/approve_routes
// 从节点的批准路由列表中移除路由（等同于禁用）
```

## 📁 新增文件

### `src/lib/common/api/version.ts`
包含版本检测逻辑：
- `isHeadscaleV026OrLater()` - 检测是否为 v0.26+
- `resetVersionCache()` - 重置版本缓存
- `getCachedVersion()` - 获取缓存的版本信息

## 🔄 向后兼容性保证

### 用户界面
- ✅ 所有 UI 组件保持不变
- ✅ 用户体验完全一致
- ✅ 无需用户手动配置版本

### API 接口
- ✅ 所有公共 API 函数签名保持不变
- ✅ 返回的数据结构保持一致
- ✅ 错误处理机制统一

### 数据结构
- ✅ `Route` 对象结构保持不变
- ✅ 在 v0.26+ 中从节点数据合成 Route 对象
- ✅ 所有现有代码无需修改

## 🧪 测试场景

### 测试 v0.25.x 兼容性
1. 启动 headscale v0.25.x
2. 访问 headscale-admin
3. 验证路由功能正常工作
4. 检查控制台无错误

### 测试 v0.26.0+ 兼容性
1. 启动 headscale v0.26.0+
2. 访问 headscale-admin
3. 验证路由功能正常工作
4. 检查控制台无错误

### 测试版本切换
1. 从 v0.25.x 升级到 v0.26.0+
2. 刷新 headscale-admin 页面
3. 验证自动检测到新版本
4. 验证所有功能正常工作

## 🔧 开发者信息

### 版本检测代码示例
```typescript
import { isHeadscaleV026OrLater } from '$lib/common/api';

// 在任何地方检测版本
const isNewVersion = await isHeadscaleV026OrLater();
if (isNewVersion) {
    // 使用 v0.26+ API
} else {
    // 使用 v0.25 API
}
```

### 添加新的版本特定功能
```typescript
export async function newFeature() {
    const isV026 = await isHeadscaleV026OrLater();
    
    if (isV026) {
        // v0.26+ 实现
        return await newV026Implementation();
    } else {
        // v0.25 实现或回退
        return await legacyImplementation();
    }
}
```

## 🚨 注意事项

### 性能考虑
- 版本检测只在首次调用时执行
- 结果被缓存以避免重复检测
- 如果切换服务器，需要调用 `resetVersionCache()`

### 错误处理
- 网络错误会被正确处理
- 版本检测失败时会假设为 v0.26+
- 所有 API 调用都有适当的错误处理

### 限制
- 不支持 headscale v0.25.0 之前的版本
- 版本检测依赖于 Routes API 的存在性
- 需要网络连接进行版本检测

## 📈 未来计划

1. **更精确的版本检测**：可能通过 `/api/v1/version` 端点（如果存在）
2. **版本特定优化**：为不同版本提供优化的实现
3. **自动迁移提示**：当检测到版本升级时提供用户提示

## ✅ 兼容性矩阵

| headscale 版本 | 支持状态 | API 使用 | 备注 |
|---------------|---------|----------|------|
| v0.24.x 及更早 | ❌ 不支持 | - | 请升级到 v0.25+ |
| v0.25.x | ✅ 完全支持 | Routes API | 原始实现 |
| v0.26.0+ | ✅ 完全支持 | Node API | 新实现 |

---

**更新时间**: 2025-06-16  
**兼容版本**: headscale v0.25.x 和 v0.26.0+  
**自动检测**: 是  
**用户配置**: 无需配置
