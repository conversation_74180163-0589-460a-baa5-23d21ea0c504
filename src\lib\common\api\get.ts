import { API_URL_NODE, API_URL_POLICY, API_URL_PREAUTHKEY, API_URL_ROUTES, API_URL_USER, apiGet } from '$lib/common/api';
import type {
	ApiNodes,
	ApiPolicy,
	ApiPreAuthKeys,
	ApiRoutes,
	ApiUsers,
	Node,
	NodeRoute,
	PreAuthKey,
	Route,
	User,
} from '$lib/common/types';

export async function getPreAuthKeys(
	usernames?: string[],
	init?: RequestInit,
): Promise<PreAuthKey[]> {
	if (usernames == undefined) {
		usernames = (await getUsers(init)).map((u) => u.name);
	}
	const promises: Promise<ApiPreAuthKeys>[] = [];
	let preAuthKeysAll: PreAuthKey[] = [];

	usernames.forEach(async (username: string) => {
		if(username != ""){
			promises.push(
				apiGet<ApiPreAuthKeys>(API_URL_PREAUTHKEY + '?user=' + username, init),
			);
		}
	});

	promises.forEach(async (p) => {
		const { preAuthKeys } = await p;
		preAuthKeysAll = preAuthKeysAll.concat(preAuthKeys);
	});

	await Promise.all(promises);
	return preAuthKeysAll;
}

type GetUserOptions = 
	{id: string, name?: never, email?: never} |
	{id?: never, name: string, email?: never} |
	{id?: never, name?: never, email: string}

export async function getUsers(init?: RequestInit, options?: GetUserOptions): Promise<User[]> {
	let url = API_URL_USER;
	if (options !== undefined){
		if(options.id !== undefined) {
			url += "?id=" + options.id
		} else if (options.name !== undefined) {
			url += "?name=" + options.name
		} else if (options.email !== undefined) {
			url += "?email=" + options.email
		} else {
			throw new Error("Invalid User Parameters")
		}
	}
	const { users } = await apiGet<ApiUsers>(url, init);
	return users;
}

export async function getNodes(): Promise<Node[]> {
	const { nodes } = await apiGet<ApiNodes>(API_URL_NODE);
	return nodes;
}

import { isHeadscaleV026OrLater } from './version';

// Backward compatible function that works with both v0.25 and v0.26+
export async function getRoutes(): Promise<Route[]> {
	const isV026 = await isHeadscaleV026OrLater();

	if (isV026) {
		// v0.26+: Extract routes from node data
		const nodes = await getNodes();
		const routes: Route[] = [];

		nodes.forEach(node => {
			// Create legacy Route objects from node route data
			const allRoutes = [
				...(node.approvedRoutes || []),
				...(node.availableRoutes || []),
				...(node.subnetRoutes || [])
			];

			// Remove duplicates
			const uniqueRoutes = [...new Set(allRoutes)];

			uniqueRoutes.forEach((prefix, index) => {
				const route: Route = {
					id: `${node.id}-${index}`, // Generate synthetic ID
					createdAt: node.createdAt,
					deletedAt: '',
					node: node,
					machine: undefined as never,
					prefix: prefix,
					advertised: (node.availableRoutes || []).includes(prefix),
					enabled: (node.approvedRoutes || []).includes(prefix),
					isPrimary: (node.subnetRoutes || []).includes(prefix)
				};
				routes.push(route);
			});
		});

		return routes;
	} else {
		// v0.25: Use the original routes API
		const { routes } = await apiGet<ApiRoutes>(API_URL_ROUTES);
		return routes;
	}
}

// Helper function to extract route information from a node (v0.26+)
export function getNodeRoutes(node: Node): NodeRoute[] {
	const routes: NodeRoute[] = [];

	// Get all unique routes from the node
	const allRoutes = new Set([
		...(node.approvedRoutes || []),
		...(node.availableRoutes || []),
		...(node.subnetRoutes || [])
	]);

	allRoutes.forEach(prefix => {
		const route: NodeRoute = {
			nodeId: node.id,
			prefix: prefix,
			approved: (node.approvedRoutes || []).includes(prefix),
			available: (node.availableRoutes || []).includes(prefix),
			subnet: (node.subnetRoutes || []).includes(prefix)
		};
		routes.push(route);
	});

	return routes;
}

export async function getPolicy(): Promise<string> {
	const { policy } = await apiGet<ApiPolicy>(API_URL_POLICY)
	return policy
}
