import { describe, it, expect, vi, beforeEach } from 'vitest';
import { getRoutes, getNodeRoutes, approveNodeRoutes } from './get';
import { approveNodeRoutes as modifyApproveNodeRoutes } from './modify';
import type { Node, Route, NodeRoute } from '../types';

// Mock the API functions
vi.mock('./base', () => ({
  apiGet: vi.fn(),
  apiPost: vi.fn(),
}));

describe('Routes API v0.26+ compatibility', () => {
  const mockNode: Node = {
    id: '1',
    machineKey: 'test-machine-key',
    nodeKey: 'test-node-key',
    discoKey: 'test-disco-key',
    ipAddresses: ['**********'],
    name: 'test-node',
    user: {
      id: '1',
      name: 'test-user',
      createdAt: '2023-01-01T00:00:00Z',
      displayName: 'Test User',
      email: '<EMAIL>',
      providerId: '',
      provider: '',
      profilePicUrl: ''
    },
    lastSeen: '2023-01-01T00:00:00Z',
    lastSuccessfulUpdate: '2023-01-01T00:00:00Z',
    expiry: null,
    preAuthKey: null,
    createdAt: '2023-01-01T00:00:00Z',
    registerMethod: 'REGISTER_METHOD_CLI',
    forcedTags: [],
    invalidTags: [],
    validTags: [],
    givenName: 'test-node',
    online: true,
    approvedRoutes: ['10.0.0.0/24'],
    availableRoutes: ['10.0.0.0/24', '***********/24'],
    subnetRoutes: ['10.0.0.0/24']
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('getNodeRoutes', () => {
    it('should extract route information from node data', () => {
      const routes = getNodeRoutes(mockNode);
      
      expect(routes).toHaveLength(2); // 10.0.0.0/24 and ***********/24
      
      const route1 = routes.find(r => r.prefix === '10.0.0.0/24');
      expect(route1).toEqual({
        nodeId: '1',
        prefix: '10.0.0.0/24',
        approved: true,
        available: true,
        subnet: true
      });

      const route2 = routes.find(r => r.prefix === '***********/24');
      expect(route2).toEqual({
        nodeId: '1',
        prefix: '***********/24',
        approved: false,
        available: true,
        subnet: false
      });
    });

    it('should handle nodes with no routes', () => {
      const nodeWithoutRoutes: Node = {
        ...mockNode,
        approvedRoutes: undefined,
        availableRoutes: undefined,
        subnetRoutes: undefined
      };

      const routes = getNodeRoutes(nodeWithoutRoutes);
      expect(routes).toHaveLength(0);
    });

    it('should handle empty route arrays', () => {
      const nodeWithEmptyRoutes: Node = {
        ...mockNode,
        approvedRoutes: [],
        availableRoutes: [],
        subnetRoutes: []
      };

      const routes = getNodeRoutes(nodeWithEmptyRoutes);
      expect(routes).toHaveLength(0);
    });
  });

  describe('Legacy Route compatibility', () => {
    it('should create legacy Route objects from node data', async () => {
      const { apiGet } = await import('./base');
      vi.mocked(apiGet).mockResolvedValue({ nodes: [mockNode] });

      const routes = await getRoutes();
      
      expect(routes).toHaveLength(2);
      
      const route1 = routes.find(r => r.prefix === '10.0.0.0/24');
      expect(route1).toMatchObject({
        prefix: '10.0.0.0/24',
        advertised: true,
        enabled: true,
        isPrimary: true,
        node: mockNode
      });

      const route2 = routes.find(r => r.prefix === '***********/24');
      expect(route2).toMatchObject({
        prefix: '***********/24',
        advertised: true,
        enabled: false,
        isPrimary: false,
        node: mockNode
      });
    });
  });

  describe('Route approval', () => {
    it('should approve routes using the new API', async () => {
      const { apiPost } = await import('./base');
      const updatedNode = { ...mockNode, approvedRoutes: ['10.0.0.0/24', '***********/24'] };
      vi.mocked(apiPost).mockResolvedValue({ node: updatedNode });

      const result = await modifyApproveNodeRoutes('1', ['10.0.0.0/24', '***********/24']);
      
      expect(apiPost).toHaveBeenCalledWith(
        '/api/v1/node/1/approve_routes',
        { routes: ['10.0.0.0/24', '***********/24'] }
      );
      expect(result).toEqual(updatedNode);
    });
  });
});
