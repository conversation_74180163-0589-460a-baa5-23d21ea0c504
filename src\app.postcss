@tailwind base;
@tailwind components;
@tailwind utilities;
@tailwind variants;

/*
html,
body {
	@apply h-full overflow-scroll;
}
*/
html,
body {
	@apply overflow-y-scroll;
}

/* modern theme */
@font-face {
	font-family: 'Quicksand';
	src: url('/fonts/Quicksand.ttf');
	font-display: swap;
}
/* rocket theme */
@font-face {
	font-family: 'Space Grotesk';
	src: url('/fonts/SpaceGrotesk.ttf');
	font-display: swap;
}
/* gold-nouveau theme */
@font-face {
	font-family: 'Quicksand';
	src: url('/fonts/Quicksand.ttf');
	font-display: swap;
}
/* vintage theme */
@font-face {
	font-family: 'Abril Fatface';
	src: url('/fonts/AbrilFatface.ttf');
	font-display: swap;
}
/* seafoam theme */
@font-face {
	font-family: 'Playfair Display';
	src: url('/fonts/PlayfairDisplay-Italic.ttf');
	font-display: swap;
}
